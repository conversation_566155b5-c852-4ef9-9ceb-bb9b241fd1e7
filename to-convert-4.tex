\begin{frame}{}
	\frametitle{指数表示下的乘除运算}
	\transfade
	\expositionblock{证明}{
	利用三角函数的积化和差，得到
	\begin{align}
		z_1 z_2
		= & \ r_1 r_2 [\cos (\theta_1 + \theta_2) + i \sin (\theta_1 + \theta_2)]
		= r_1 r_2 e^{i (\theta_1 + \theta_2)} \ .
	\end{align}
	}
\end{frame}


\begin{frame}{}
	\frametitle{指数表示下的乘除运算}
	\transfade
	\expositionblock{证明}{
	对于除法，
	\begin{align}
		\frac{z_1}{z_2} = & \ \frac{r_1 \cos \theta_1 + i r_1 \sin \theta_1}{r_2 \cos \theta_2 + i r_2 \sin \theta_2}\\
		= & \ \frac{r_1 r_2 \cos\theta_1 \cos \theta_2 + r_1 r_2 \sin\theta_1 \sin \theta_2}{r_2^2\cos^2\theta_2 + r_2^2\sin^2\theta_2} \\
		& \ + \frac{- r_1 r_2 \cos\theta_1 \sin \theta_2 + r_1 r_2 \sin\theta_1 \cos \theta_2}{r_2^2\cos^2\theta_2 + r_2^2\sin^2\theta_2}\\
		= & \ \frac{r_1 r_2 \cos(\theta_1 - \theta_2)}{r_2^2}
		+ \frac{r_1 r_2 \sin (\theta_1 - \theta_2)}{r_2^2} = \frac{r_1}{r_2} e^{i (\theta_1 - \theta_2)} \ .
	\end{align}
	}
\end{frame}


\begin{frame}{}
	\frametitle{总结}
	\transfade
	\begin{itemize}
		\item<+-> 三种常用表示：代数表示、三角表示、指数表示
		\begin{align}
			z = x + i y = r \cos\theta + i r \sin \theta = r e^{i \theta} \ .
		\end{align}
		\item<+-> 模长 $|z|$
		\begin{align}
			|z| = r = \sqrt{(r \cos\theta)^2 + (r \sin \theta)^2} = \sqrt{x^2 + y^2}
			= \sqrt{ z \bar z}
		\end{align}
		模方 (modulus squared)
		\begin{align}
			|z|^2 = z \bar z \ .
		\end{align}
	\end{itemize}
\end{frame}





\begin{frame}{}
	\frametitle{总结}
	\transfade
	\begin{itemize}
		\item<+-> $| - z| = |z|$
		\item<+-> $|e^{i \theta}| = 1$，其中 $\theta \in \mathbb{R}$
		\item<+-> $|z_1 z_2| = |z_1| |z_2|$
	\end{itemize}
\end{frame}





\begin{frame}{}
	\frametitle{代数基本定理}
	\transfade
	\begin{itemize}
		\item<+-> \bluebox{代数基本定理}：任何一个 $n$-次复系数多项式都有 $n$ 个复数根
		\commentblock{重根}{
		这 $n$ 个复数根可能有 \bluebox{重复}。
		}
		\commentblock{因式分解}{
		$n$-次复系数多项式 $a_n x^n + a_{n - 1}z^{n - 1} + ... + a_0$ 一定可以分解为
		\begin{equation}
			P(z) = a_n(z - z_1) (z - z_2) \cdots (z - z_n) \ ,
		\end{equation}
		其中 $z_i$ 为根。
		}
	\end{itemize}
\end{frame}




\begin{frame}{}
	\frametitle{代数基本定理}
	\transfade
	\begin{itemize}
		\item<+-> \bluebox{韦达定理}：对任意复多项式 $P(z) = a_n z^n + a_{n - 1}z^{n - 1} + \cdots + a_0$ 的根 $z_1, \cdots, z_n$ 满足
		\begin{equation}
			(-1)^n a_n \prod_{k = 1}^{n}z_k = a_0, \qquad 
			a_n\sum_{k = 1}^{n}z_k = - a_{n - 1} \ .
		\end{equation}
	\end{itemize}
\end{frame}


\begin{frame}{}
	\frametitle{无穷远点}
	\transfade
	\begin{itemize}
		\item<+-> $\mathbb{R}$ 与无穷远点
		\begin{center}
			\href{./animation/ExtendedR1.html}{
				\includegraphics[width=0.6\textwidth]{animation/ExtendedR1.png}
			}
		\end{center}
		\item<+-> $\mathbb{R} \cup \infty$ 实际上就是一维圆圈 $S^1$。
	\end{itemize}
\end{frame}



\begin{frame}{}
	\frametitle{无穷远点}
	\transfade
	\begin{itemize}
		\item<+-> $\mathbb{C}$ 与无穷远点 $\infty$ 合并为 \greenbox{扩充的复平面}
		\item<+-> $\mathbb{C} \cup \{\infty\}$ 实际上就是二维球面 $S^2$。
	\end{itemize}
\end{frame}

\begin{frame}[t]\frametitle{无穷远点}
\begin{figure}
	\centering
	\includegraphics{image/extended-C.pdf}
\end{figure}


\end{frame}



{\setbeamertemplate{background}{\includegraphics[width=\paperwidth]{image/section-title-pink.png}}%
\begin{frame}

\begin{center}
	\vspace{-0.8em}
	\Huge{\color{white}{\textbf{{三}}}}

	\vspace{0.7em}
	\huge{\color{white}{\textbf{{点集基础}}}}
\end{center}
\end{frame}
