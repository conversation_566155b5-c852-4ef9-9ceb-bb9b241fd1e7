\begin{frame}{}
	\frametitle{共轭调和函数与静电场}
	\transfade
	\commentblock{平面电场}{
	\redbox{如何形成二维电场？}

	\vspace{1em}
	可以使用无穷长直均匀带电直线
	\begin{figure}
		\includegraphics{image/infinitely-long-charged-line.pdf}
	\end{figure}
	}
\end{frame}




\begin{frame}{}
	\frametitle{初等函数}
	\transfade
	\begin{itemize}
		\item<+-> \greenbox{初等函数}：常数、幂函数、指数函数、对数函数、三角函数、反三角函数，及其有限次加减乘除、复合所构成的函数。
		\item<+-> 导数公式与大一的内容一样
	\end{itemize}
\end{frame}


\begin{frame}{}
	\frametitle{初等函数：整幂函数}
	\transfade
	\begin{itemize}
		\item<+-> \bluebox{整幂} 复函数是全 $\mathbb{C}$ 解析的函数，定义为
		\begin{align}
			z^{n = 0, 1,2,3, \ldots} = \underbrace{z \cdot z \cdot \ldots \cdot z}_{n\text{ 个}} \ , \qquad
			z^{n = -1, -2, \ldots} = \frac{1}{z^{n}} \ .
		\end{align}
		\commentblock{确定性}{
		给定一个 $z \in \mathbb{C}$，则 $z^n$ 是唯一 \bluebox{确定} (单值) 的。
		}
		\item<+-> 满足整幂实函数的性质，
		\begin{equation}
			(z_1 z_2)^n = z_1^n z_2^n \ , \qquad (z_1 + z_2)^n = \sum_{k = 0}^{n}C_n^k z_1^k z_2^{n - k} \ .
		\end{equation}
		\item<+-> 若 $z = re^{i \theta}$，则 $z^n = r^n e^{i n \theta}$。
	\end{itemize}
\end{frame}



\begin{frame}{}
	\frametitle{初等函数：多项式}
	\transfade
	\begin{itemize}
		\item<+-> 整幂函数通过复系数可以组合成 $n$-次 多项式，
		\begin{align}
			P(z) = \sum_{k = 0}^{n} a_k z^k \ , \qquad a_k \in \mathbb{C} \ , \quad a_n \ne 0\ .
		\end{align}
		\item<+-> 比如 $P(z) = z^2 - 1$，$P(z) = z^3 - 3 z^2 + 4 + 3 i$.
		\item<+-> \bluebox{代数基本定理}：任意 $n$-次多项式都有 $n$ 个复数根。
		\begin{equation}
			P(z) = a_n \prod_{k = 1}^{n} (z - z_k) \ .
		\end{equation}
	\end{itemize}
\end{frame}

\begin{frame}{}
	\frametitle{初等函数：多项式}
	\transfade
	\begin{itemize}
		\item<+-> 实数多项式 $P_\mathbb{R}(x) = \sum_{k = 0}^{n} r_k x^k $ 不一定有 $n$ 个 \bluebox{实数根}。比如
	\begin{align}
		P(x) = x^2 + 1 \ .
	\end{align}

	\item<+-> 倘若允许 \bluebox{复数根}，则 $x^2 + 1$ 恰好有两个根：

	\visible<+->{$+i$, $-i$}

	\visible<+->{$x^2 + 1 = (x - i)(x + i)$}
	\item<+-> 又如 $x^3 - 1$ 只有一个实根：$+1$

	\visible<+->{另外有两个复数根 $e^{\frac{2 \pi i}{3}}$, $e^{\frac{4 \pi i}{3}}$}

	\visible<+->{$x^3 - 1 = (z - 1)(z - e^{\frac{2\pi i}{3}})(z - e^{\frac{4\pi i}{3}})$}
	\end{itemize}
\end{frame}



\begin{frame}{}
	\frametitle{初等函数：次方根函数}
	\transfade
	\begin{itemize}
		\item<+-> 给定一个 $z \in \mathbb{C}$ 以及 $n \in \mathbb{N}_{\ge 1}$，若 $z_0 \in \mathbb{C}$ 满足 $z_0^n = z$，则称 $z_0$ 为 $z$ 的一个 \greenbox{$n$-次方根}，记作 $z^{\frac{1}{n}}$。
		\item<+-> \bluebox{定理}：若 $z = re^{i \theta}$，则 $z_0 = r^{\frac{1}{n}}e^{i \frac{\theta}{n}}$ 是 $z$ 的一个 $n$-次方根。
	\end{itemize}
\end{frame}


\begin{frame}{}
	\frametitle{初等函数：次方根函数}
	\transfade
	\begin{itemize}
		\item<+-> \bluebox{多值性定理}：给定 $z$ 之后，$n$-次方根 $z_0$ 有 \bluebox{$n$ 个} 可能值。
		\expositionblock{多值性}{
		设 $z_0$ 是一个 $n$-次方根，即 $z_0^n = z$。则必然有
		\begin{align}
			z_0, \qquad e^{\frac{2\pi i}{n}}z_0, \qquad e^{\frac{2\pi i}{n}\cdot 2} z_0, \qquad \cdots, \qquad e^{\frac{2\pi i}{n}(n - 1)}z_0
		\end{align}
		都是 $n$-次方根，因为 $(e^{\frac{2 \pi i}{n} k })^n = e^{2\pi i k} = 1$，$k = 0, 1, \dots, n - 1$。
		}
	\end{itemize}
\end{frame}


\begin{frame}{}
	\frametitle{初等函数：次方根函数}
	\transfade
	\begin{itemize}
		\item<+-> $n$-次方根的多值性来源于 \bluebox{$z$ 的辐角} 的多值性
		\begin{align}
			& \ z = re^{i \theta} = r e^{i (\theta + 2\pi k)} \\
		\Rightarrow & \ z^{\frac{1}{n}} = r^{\frac{1}{n}} e^{i \frac{\theta}{n}},\qquad 
		\text{或者}, \qquad r^{\frac{1}{n}} e^{i (\frac{\theta}{n} + \frac{2\pi k}{n})} \ .
		\end{align}
	\end{itemize}
\end{frame}



\begin{frame}{}
	\frametitle{初等函数：次方根函数}
	\transfade
	\begin{itemize}
		\item<+-> $1$ 的 3 次方根
		\begin{align}
			& \ 1 = e^{0\pi i} = e^{2\pi i} = e^{4 \pi i}\\
			\Rightarrow & \ 
			1^{\frac{1}{3}} = 1, e^{\frac{2\pi i}{3}}, e^{\frac{4\pi i}{3}} \ .
		\end{align}
		\item<+-> 多项式方程 $x^3 - 1 = 0$ 的解即为 $1$ 的 3 次方根。
		\item<+-> $1$ 的 $n$ 次方根
		\begin{align}
			& \ 1 = e^{0\pi i} = e^{2\pi i} = \cdots = e^{2 \pi i (n - 1)}\\
			\Rightarrow & \ 
			1^{\frac{1}{n}} = 1, e^{\frac{2\pi i}{n}}, \cdots, e^{\frac{2\pi i (n - 1)}{n}} \ .
		\end{align}
	\end{itemize}
\end{frame}
