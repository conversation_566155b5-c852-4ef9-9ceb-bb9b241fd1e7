\begin{frame}{}
	\frametitle{复导数}
	\transfade
	\begin{itemize}
		\item $z$ 与 $\bar z$ \redbox{不独立}：$z \to z + \Delta \Rightarrow \bar z \to \bar z + \overline{\Delta z}$。
		\item 但是，偏导算符呈现 \bluebox{形式上独立性 (formal independence)}，
		\begin{align}
			\frac{\partial \bar z}{\partial z} = \frac{\partial z}{\partial \bar z} = 0, \qquad
			\frac{\partial z}{\partial z} = \frac{\partial \bar z}{\partial \bar z} = 1\ .
		\end{align}
		\item<+-> \bluebox{形式上}，$\partial_z, \partial_{\bar z}$ 跟「偏导数」完全一样：接受它们作为偏导存在，$z, \bar z$ \bluebox{形式上独立}
		\item<+-> 通常：$f$ 满足 CR 条件/解析性/全纯性 $\Leftrightarrow$ $f$ \redbox{不含} $\bar z$ $\Leftrightarrow$ $\partial_{\bar z} f = 0$
		\commentblock{奇点}{
		在 \bluebox{奇点} 处，$f$ 只依赖 $z$ $\not\Rightarrow$ $\partial_{\bar z} f = 0$。
		}
		
	\end{itemize}
\end{frame}





\begin{frame}{}
	\frametitle{复导数}
	\transfade
	\begin{itemize}
		\item<+-> 若 $f$ 解析/全纯，则其 \bluebox{复共轭} $\overline{f(z)}$ 满足
		\begin{align}
			\partial_z \overline{f(z)} = 0\ .
		\end{align}
		这样的函数称为 \greenbox{反全纯 (anti-holomorphic) 函数}。
		\item<+-> 一般的复变函数既不是全纯也不是反全纯函数。可以用 $f(z, \bar z)$ 来标明/强调。
	\end{itemize}
\end{frame}



\begin{frame}{}
	\frametitle{共轭调和函数}
	\transfade
	\begin{itemize}
		\item<+-> 区域 $D$ 上解析的函数 $f = u + i v$ 的实部和虚部满足 CR 条件
		\begin{align}
			\frac{\partial u}{\partial x} = \frac{\partial v}{\partial y}, \qquad
			\frac{\partial u}{\partial y} = - \frac{\partial v}{\partial x} \ .
		\end{align}
		\item<+-> 对这些方程两边作 $\partial_x$ 和 $\partial_y$
		\begin{align}
			\partial_x: \frac{\partial^2 u}{\partial x^2} = & \ \frac{\partial^2 v}{\partial x\partial y}, 
			& \frac{\partial^2 u}{\partial x\partial y} = & \ - \frac{\partial^2 v}{\partial x^2} \\
			\partial_y: \frac{\partial^2 u}{\partial y\partial x} = & \ \frac{\partial^2 v}{\partial y^2}, 
			&\frac{\partial^2 u}{\partial y^2} = & \ - \frac{\partial^2 v}{\partial y\partial x} \ .
		\end{align}
		\item<+-> 稍作比较可得两条 \greenbox{调和 (harmonic) 方程}
		\begin{align}
			\frac{\partial^2 u}{\partial x^2} + \frac{\partial^2 u}{\partial y^2} = 0, \qquad
			\frac{\partial^2 v}{\partial x^2} + \frac{\partial^2 v}{\partial y^2} = 0
		\end{align}
		
	\end{itemize}
\end{frame}



\begin{frame}{}
	\frametitle{共轭调和函数}
	\transfade
	\begin{itemize}
		\item<+-> 解析函数的实部和虚部均为 \greenbox{调和函数}，而且二者由 CR 条件相互锁定，形成一对 \greenbox{共轭调和函数}。
		\item<+-> 从一个调和函数 $u$ (或 $v$) 出发，可以 \bluebox{通过 CR 条件} 解出与之共轭的调和同伴 $v$ (或 $u$)，并由此获得 \bluebox{解析函数}。
	\end{itemize}
\end{frame}



\begin{frame}{}
	\frametitle{共轭调和函数：例子}
	\transfade
	\begin{itemize}
		\item<+-> 设 $u(x,y) = x^2 - y^2$。若 $v(x,y)$ 是是与之共轭的调和函数，则必然有
		\begin{align}
			dv
			= & \ \frac{\partial v}{\partial x}dx + \frac{\partial v}{\partial y} dy
			= - \frac{\partial u}{\partial y}dx + \frac{\partial u}{\partial x} dy \\
			= & \ + 2y dx + 2x dy = d(2 xy) \ .
		\end{align}
		\item<+-> 于是 $v = 2 xy + C$，而 $C$ 是 \greenbox{任意实常数}。
		\item<+-> 从而 $f(z) = x^2 - y^2 + 2i xy + Ci = (x + i y)^2 + C i $ 是 \bluebox{解析函数}：
		\begin{align}
			f = z^2 + C \highlight{titleblue}{i} \ .
		\end{align}
	\end{itemize}
\end{frame}



% \begin{frame}{}
% 	\frametitle{共轭调和函数：例子}
% 	\transfade
% 	\begin{itemize}
% 		\item<+-> 设 $u(x,y) = x^2 - y^2$。若 $v(x,y)$ 是是与之共轭的调和函数，则必然有
% 		\begin{align}
% 			dv
% 			= & \ \frac{\partial v}{\partial x}dx + \frac{\partial v}{\partial y} dy
% 			= - \frac{\partial u}{\partial y}dx + \frac{\partial u}{\partial x} dy \\
% 			= & \ + 2y dx + 2x dy = d(2 xy) \ .
% 		\end{align}
% 		\item<+-> 于是 $v = 2 xy + C$，而 $C$ 是任意实常数。
% 		\item<+-> 从而 $f() = x^2 - y^2 + 2xy + C = (x + i y)^2 + C$ 是 \bluebox{解析函数}：
% 		\begin{align}
% 			f = z^2 + C \ .
% 		\end{align}
% 	\end{itemize}
% \end{frame}




\begin{frame}{}
	\frametitle{共轭调和函数}
	\transfade
	\begin{itemize}
		\item<+-> 区域 $D$ 中共轭调和函数的 \bluebox{线积分} 求解法：
		\begin{align}
			& \ dv
			= \frac{\partial v}{\partial x}dx + \frac{\partial v}{\partial y} dy
			= - \frac{\partial u}{\partial y}dx + \frac{\partial u}{\partial x} dy \\
			\Rightarrow & \ v = \int_{(x_0, y_0)}^{(x,y)} dv + C
			= \int_{(x_0, y_0)}^{(x,y)}  \left[- \frac{\partial u}{\partial y}dx + \frac{\partial u}{\partial x} dy\right] + C \ .
		\end{align}
		\commentblock{路径无关性}{
		上述积分只指定了初始点 $(x_0, y_0)$ 和终末点 $(x, y)$。
		}
	\end{itemize}
\end{frame}



\begin{frame}{}
	\frametitle{共轭调和函数}
	\transfade
	\begin{itemize}
		\item<+-> 区域 $D$ 中共轭调和函数的 \bluebox{线积分} 求解法：
		\begin{align}
			v = \int_{(x_0, y_0)}^{(x,y)} dv + C
			= \int_{(x_0, y_0)}^{(x,y)}  \left[- \frac{\partial u}{\partial y}dx + \frac{\partial u}{\partial x} dy\right] + C \ .
		\end{align}
		\commentblock{路径无关性}{
		\redbox{具体路径怎么选？}
		\begin{figure}
			\includegraphics{image/three-paths.pdf}
		\end{figure}
		}
	\end{itemize}
\end{frame}



\begin{frame}{}
	\frametitle{共轭调和函数}
	\transfade
	\begin{itemize}
		\item<+-> 区域 $D$ 中共轭调和函数的线积分求解法：
		\begin{align}
			v = \int_{(x_0, y_0)}^{(x,y)} dv + C
			= \int_{(x_0, y_0)}^{(x,y)}  \left[- \frac{\partial u}{\partial y}dx + \frac{\partial u}{\partial x} dy\right] + C \ .
		\end{align}
		\commentblock{路径几乎无关性}{
		绿色积分 $-$ 紫色积分 $=$ 闭合路径 $C$ 积分：
		\begin{align}
			& \ \oint_C \left[\highlight{titleblue}{- \frac{\partial u}{\partial y}}dx + \highlight{orange}{\frac{\partial u}{\partial x}}dy\right] =
			\oint [\highlight{titleblue}{P} dx + \highlight{orange}{Q} dy] \\
			\eqtext{Green's} & \ \int \left[
			\frac{\partial}{\partial x} Q - \frac{\partial}{\partial y}P 
			\right]dx dy
			= \int \left[
			\frac{\partial^2 u}{\partial x^2} +
			\frac{\partial^2 u}{\partial y^2}
			\right]dx dy = 0 \ .
		\end{align}
		}
	\end{itemize}
\end{frame}


\begin{frame}{}
	\frametitle{共轭调和函数}
	\transfade
	\begin{itemize}
		\item<+-> 区域 $D$ 中共轭调和函数的线积分求解法：
		\begin{align}
			v = \int_{(x_0, y_0)}^{(x,y)} dv + C
			= \int_{(x_0, y_0)}^{(x,y)}  \left[- \frac{\partial u}{\partial y}dx + \frac{\partial u}{\partial x} dy\right] + C \ .
		\end{align}
		\commentblock{路径几乎无关性}{
		蓝色积分 $-$ 绿色积分 $=$ 与 $(x, y), (x_0, y_0)$ 无关的常数 $C$：不要紧。

		\vspace{1em}

		结论：随便选一条连接 $(x_0, y_0)$ 和 $(x,y)$ 的路径，只要 \bluebox{完全在解析区内}。
		}
	\end{itemize}
\end{frame}
