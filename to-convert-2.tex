\begin{frame}{}
	\frametitle{无理数}
	\transfade
	\begin{itemize}
		\item<+-> 无理数 (irrational numbers)
		\begin{align}
			e, \qquad \pi, \qquad \sqrt{2}, \qquad \sqrt{3}, \qquad \ldots
		\end{align}

	\end{itemize}
\end{frame}


\begin{frame}{}
	\frametitle{无理数}
	\transfade
	\begin{itemize}
		\item<+-> 毕达哥拉斯学派的数学家 Hippasus 提出 $\sqrt{2}$ 是一种之前没有研究过的数：\bluebox{无理数}。毕达哥拉斯认为「数是绝对的、万物皆数 (all is number)」，拒绝接受无理数的存在，对 Hippasus 处以 \redbox{死刑} (推海里淹死)。
		\begin{center}
			\includegraphics[height=0.4\textheight]{image/Hippasus.png}
		\end{center}
		\item<+-> 公元前一世纪左右，《九章算术》记载了开方运算中出现的无理数
	\end{itemize}
\end{frame}


\begin{frame}{}
	\frametitle{无理数}
	\transfade
	\begin{itemize}
		\item<+-> 在 16 世纪的欧洲，人们逐渐接受 \bluebox{负数}、\bluebox{分数}，到 18、19 世纪人们以 \greenbox{代数数} 和 \greenbox{超越数} 两种角度思考也研究无理数。
		\commentblock{代数数}{
		即整系数多项式的根。
		}

		18, 19世纪数学家 Lambert、Legendre、Weierstrass、Cantor、Dedekind 等都对无理数进行深入的研究
	\end{itemize}
\end{frame}




\begin{frame}{}
	\frametitle{实数}
	\transfade
	\begin{itemize}
		\item<+-> 实数 (real numbers), $\mathbb{R}$：有理数与无理数的并集
		\item<+-> 中世纪时，阿拉伯数学家提出实数的概念
		\item<+-> 「实 (real)」来自 17 世纪的笛卡尔
		\commentblock{实根与虚根}{
		17 世纪的数学家已经在研究代数方程根，分为实根 (real roots) 和虚根 (imaginary roots)
		}
	\end{itemize}
\end{frame}




\begin{frame}{}
	\frametitle{实数}
	\transfade
	\begin{itemize}
		\item<+-> 有理数和无理数密密麻麻 \bluebox{无缝} 形成实数轴 (real line)

		\begin{center}
			\includegraphics[width=0.7\textwidth]{image/real.png}
		\end{center}
		\commentblock{拓扑}{
		“密密麻麻、无缝”是一种拓扑概念
		}
	\end{itemize}
\end{frame}





{\setbeamertemplate{background}{\includegraphics[width=\paperwidth]{image/section-title-pink.png}}%
\begin{frame}

\begin{center}
	\vspace{-0.8em}
	\Huge{\color{white}{\textbf{{二}}}}

	\vspace{0.7em}
	\huge{\color{white}{\textbf{{复数}}}}
\end{center}
\end{frame}
}



\begin{frame}{}
	\frametitle{实数}
	\transfade
	\begin{itemize}
		\item<+-> 实数及其加、乘运算满足基本性质：对任意 $a, b \in \mathbb{R}$
		\begin{itemize}
			\item<+-> 加法 \bluebox{交换} 律 $a + b = b + a$，\bluebox{结合} 律 $(a + b) + c = a + (b + c)$
			\item<+-> 乘法 \bluebox{交换} 律 $a \cdot b = b a$，\bluebox{结合} 律 $(a \cdot b) \cdot c = a \cdot (b \cdot c)$
			\item<+-> 加乘 \bluebox{分配} 律：$a \cdot (b + c) = a b + a c$
			\item<+-> 有加法 \greenbox{单位元} $0 \in \mathbb{R}$：$0 + a = 0 + a = a$
			\item<+-> 有乘法 \greenbox{单位元} $1 \in \mathbb{R}$：$1 \cdot a = a \cdot 1 = a$
			\item<+-> 有乘法 \bluebox{零} 元 $0$：$a \cdot 0 = 0 \cdot a = 0$
			\item<+-> 存在加法 \greenbox{逆元} $- a$：$(-a) + a = 0 $
			\item<+-> 当 $a \ne 0$，存在乘法 \greenbox{逆元} $a^{-1}$：$a^{-1} \cdot a = 1 $
		\end{itemize}
	\end{itemize}
\end{frame}




\begin{frame}{}
	\frametitle{复数}
	\transfade
	\begin{itemize}
		\item<+-> 问题一：是否有别的非实数的东西满足上面这些运算性质？
		\item<+-> 问题二：非常简单的实 (整) 系数代数方程 $x^2 + 1 = 0$ 是否有解？
		\item<+-> 目标：扩充实数，使得
		\begin{itemize}
			\item<+-> A: 维持实数的代数性质
			\item<+-> B: 使得实方程 $x^2 + 1 = 0$ 有解
		\end{itemize}
	\end{itemize}
\end{frame}



\begin{frame}{}
	\frametitle{复数}
	\transfade
	\begin{itemize}
		\item<+-> 定义 \greenbox{虚数单位 (imaginary unit) $i$}，实现目标 B
		\equationblock{虚数单位}{
		\begin{equation}
			i^2 + 1 = 0 \ .
		\end{equation}
		}
		\commentblock{虚数单位}{
		有的文献和编程语言使用 $j$ 或者 $I$ 代替 $i$，
		}
	\end{itemize}
\end{frame}



\begin{frame}{}
	\frametitle{复数}
	\transfade
	\begin{itemize}
		\item<+-> 定义 \greenbox{复数 (complex numbers)} 为所有形如 $a + bi$ 的物体，其中 $a, b \in \mathbb{R}$
		\item<+-> 对任意复数 $z = a + b i$，定义 \greenbox{实部 $\operatorname{Re}z \coloneqq a$}，\greenbox{虚部 $\operatorname{Im}z \coloneqq b$}
		\item<+-> 全体复数形成的集合称为 \greenbox{复数集 $\mathbb{C}$}
	\end{itemize}
\end{frame}
