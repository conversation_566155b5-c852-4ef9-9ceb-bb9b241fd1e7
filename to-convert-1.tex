\begin{frame}
	%\transfade 渐变
	\titlepage % Print the title page as the first slide
\end{frame}



% \begin{frame}{}
% 	\frametitle{课程概况}
% 	\transfade
% 	\begin{itemize}
% 		\item 课程：数学物理方法
% 		\item 任课老师：

% 		(1) 潘逸文 <EMAIL>

% 		(2) 另外两个班的任课老师：

% 		余钊焕：<EMAIL>

% 		任杰：<EMAIL>


% 		\item 本课助教：张锦鹏 <EMAIL>
% 	\end{itemize}
% \end{frame}


% \begin{frame}{}
% 	\frametitle{课程概况}
% 	\transfade
% 	\begin{itemize}
% 		\item 微信群：二维码
% 		\begin{center}
% 			\includegraphics[height=0.7\textheight]{image/qr-group-2024.jpg}
% 		\end{center}
% 	\end{itemize}
% \end{frame}


% \begin{frame}{}
% 	\frametitle{课程概况}
% 	\transfade
% 	\begin{itemize}
% 		\item 课程主页：信息与作业发布、资源分享等
% 		\begin{center}
% 			\includegraphics[height=0.7\textheight]{image/qr-website-2024.png}
% 		\end{center}
% 	\end{itemize}
% \end{frame}


% \begin{frame}{}
% 	\frametitle{课程概况}
% 	\transfade
% 	\begin{itemize}
% 		\item 考核方式：

% 		期末 (60 \%) + 平时 (随机考勤、期中 \bluebox{考察}、作业、平时表现) (40 \%)

% 		\item 作业：根据课程进度，大概 2 周 1 次。
		
% 		由助教张锦鹏批改
% 	\end{itemize}
% \end{frame}


\begin{frame}
	\frametitle{课程概况}

	\begin{itemize}
		\item<+-> 这是一门 \bluebox{数学课}：核心是数学，但是处处渗透物理思想
		\item<+-> 同时也是一门 \bluebox{语言课}：学会用复数说话，用方程描述物理，用特殊函数刻画物理演化
	\end{itemize}
	
\end{frame}


\begin{frame}{}
	\frametitle{课程概况}
	\transfade
	\begin{itemize}
		\item<+-> 数学是一门独立的、有内在生命力、独特价值评价体系的学科
		\commentblock{数学不只是工具}{
		数学为物理提供工具，但数学 \redbox{不仅仅} 是物理的工具。
		}
		\item<+-> 特别地，数学是物理的 \bluebox{语言}：没有数学，物理学家难以进行精确表达思想和传递信息
		\commentblock{普世语言}{
		甚至可能是整个多重宇宙中所有智慧生物的普世语言。
		}
	\end{itemize}
\end{frame}



\begin{frame}{}
	\frametitle{课程概况}
	\transfade
	\begin{itemize}
		\item<+-> 反过来，物理也可以为数学反哺许多新奇的思想和生成数学结构
		\commentblock{来自物理的反作用}{
		广义相对论与微分几何，散射振幅与代数几何，杨-米尔斯理论与微分拓扑，超对称与算子代数、代数簇
		}
	\end{itemize}
\end{frame}






\begin{frame}{}
	\frametitle{课程概况}
	\transfade
	\begin{itemize}
		\item<+-> 《数学物理方法》是众多后续课程和科研的基石
		\item<+-> 电动力学、量子力学：数理方程的求解

		统计力学：复变函数、级数、积分技巧

		量子场论：复变函数、积分技巧、数理方程的求解

		广义相对论：数理方程的求解

		通信与信息技术：傅里叶级数、傅里叶变换，$\delta$ 函数
		\item<+-> 现代理论物理可能需要远超本课程所涵盖的内容：微分与代数几何、抽象代数结构、复杂微分方程

	\end{itemize}
\end{frame}

\begin{frame}{}
	\frametitle{课程概况}
	\begin{itemize}
		\item<+-> 数学物理方法：主要介绍两个基本要素
		\begin{itemize}
			\item<+-> 复数与复变函数
			\item<+-> 数理方程与特殊函数 (作为数理方程的解)
		\end{itemize}
	\end{itemize}
\end{frame}



\begin{frame}{}
	\frametitle{本章概要}
	\transfade
	\begin{itemize}
		\item 数的历史

		\item 复数

		\gray{复数、复数的表示方法、复数的性质}
		\item 点集基础

		\gray{内点、边界点、聚点、特殊点集}

		\item 解析函数

		\gray{复可导性、Cauchy-Riemann 条件、多值函数与支点}
	\end{itemize}
\end{frame}




{\setbeamertemplate{background}{\includegraphics[width=\paperwidth]{image/section-title-pink.png}}%
\begin{frame}

\begin{center}
	\vspace{-0.8em}
	\Huge{\color{white}{\textbf{{一}}}}

	\vspace{0.7em}
	\huge{\color{white}{\textbf{{数的历史}}}}
\end{center}
\end{frame}
}


\begin{frame}{}
	\frametitle{自然数}
	\transfade
	\begin{itemize}
		\item<+-> 自然数 (natural numbers)，$\mathbb{N}$
		\begin{align}
			\highlight{titleblue}{0}, 1, 2, 3, 4, \ldots
		\end{align}
		\item<+-> 用于标记现实物体、事件的数量
	\end{itemize}
\end{frame}






\begin{frame}{}
	\frametitle{自然数}
	\transfade
	\begin{itemize}
		\item<+-> 原始社会用 \bluebox{绳结} 计数
	\end{itemize}
	\begin{figure}
		\centering
		\includegraphics[width=0.5\textwidth]{image/knots.jpg}
		\caption{印加文明的记事绳结：奇普 (khipu)}
	\end{figure}
\end{frame}
