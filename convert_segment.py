#!/usr/bin/env python3
"""
Script to convert LaTeX segments to Marp format according to tex2marp rules
"""

import re
import sys

def convert_latex_to_marp(content):
    """Convert LaTeX content to Marp format"""
    
    # Remove LaTeX frame structure and convert to slides
    content = re.sub(r'\\begin\{frame\}.*?\n', '---\n\n', content)
    content = re.sub(r'\\end\{frame\}', '', content)
    content = re.sub(r'\\frametitle\{([^}]+)\}', r'### \1', content)
    content = re.sub(r'\\transfade', '', content)
    
    # Convert colored boxes
    content = re.sub(r'\\greenbox\{([^}]+)\}', r'**<green>\1**', content)
    content = re.sub(r'\\redbox\{([^}]+)\}', r'**<red>\1**', content)
    content = re.sub(r'\\bluebox\{([^}]+)\}', r'**<orange>\1**', content)
    
    # Convert comment blocks
    def replace_commentblock(match):
        title = match.group(1)
        content_text = match.group(2)
        # Apply colored box conversions within comment content
        content_text = re.sub(r'\\greenbox\{([^}]+)\}', r'**<green>\1**', content_text)
        content_text = re.sub(r'\\redbox\{([^}]+)\}', r'**<red>\1**', content_text)
        content_text = re.sub(r'\\bluebox\{([^}]+)\}', r'**<orange>\1**', content_text)
        return f"<div class='proof comment'>\n\n**{title}**\n\n{content_text}\n</div>"
    
    content = re.sub(r'\\commentblock\{([^}]+)\}\{([^}]+)\}', replace_commentblock, content, flags=re.DOTALL)
    
    # Convert exposition blocks
    def replace_expositionblock(match):
        title = match.group(1)
        content_text = match.group(2)
        return f"<div class='proof'>\n\n**{title}**\n\n{content_text}\n</div>"
    
    content = re.sub(r'\\expositionblock\{([^}]+)\}\{([^}]+)\}', replace_expositionblock, content, flags=re.DOTALL)
    
    # Convert math highlighting
    content = re.sub(r'\\highlight\{red\}\{([^}]+)\}', r'\\red{\1}', content)
    content = re.sub(r'\\highlight\{titlegreen\}\{([^}]+)\}', r'\\green{\1}', content)
    content = re.sub(r'\\highlight\{titleblue\}\{([^}]+)\}', r'\\orange{\1}', content)
    
    # Convert images
    def replace_includegraphics(match):
        width_spec = match.group(1) if match.group(1) else "0.5"
        url = match.group(2)
        # Convert width from textwidth to pixels (approximate)
        try:
            width_factor = float(width_spec.replace('\\textwidth', ''))
            width_px = int(width_factor * 800)  # Assume 800px base width
        except:
            width_px = 400
        return f"![width:{width_px}px]({url})"
    
    content = re.sub(r'\\includegraphics\[width=([^]]+)\]\{([^}]+)\}', replace_includegraphics, content)
    content = re.sub(r'\\includegraphics\[height=[^]]+\]\{([^}]+)\}', r'![width:400px](\1)', content)
    content = re.sub(r'\\includegraphics\{([^}]+)\}', r'![width:400px](\1)', content)
    
    # Clean up LaTeX itemize
    content = re.sub(r'\\begin\{itemize\}', '', content)
    content = re.sub(r'\\end\{itemize\}', '', content)
    content = re.sub(r'\\item<\+->\\s*', '* ', content)
    content = re.sub(r'\\item\\s*', '* ', content)
    
    # Clean up other LaTeX commands
    content = re.sub(r'\\begin\{center\}', '', content)
    content = re.sub(r'\\end\{center\}', '', content)
    content = re.sub(r'\\begin\{figure\}', '', content)
    content = re.sub(r'\\end\{figure\}', '', content)
    content = re.sub(r'\\centering', '', content)
    content = re.sub(r'\\caption\{([^}]+)\}', r'\n\1', content)
    
    # Clean up special LaTeX constructs
    content = re.sub(r'\{\\setbeamertemplate.*?\}', '', content, flags=re.DOTALL)
    content = re.sub(r'\\rightline\{([^}]+)\}', r'\n\1', content)
    
    # Clean up extra whitespace
    content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
    content = re.sub(r'^\s+', '', content, flags=re.MULTILINE)
    
    return content

def create_marp_header():
    """Create standard Marp header"""
    return """---
marp: true
theme: rose-pine-dawn
paginate: true
_paginate: skip
size: 16:9

math: mathjax

---
$$
\\newcommand\\blue[1]{{\\color[rgb]{0.20, 0.43, 0.75}{#1}}}
\\newcommand\\red[1]{{\\color[rgb]{0.839844, 0.507813, 0.488281}{#1}}}
\\newcommand\\green[1]{{\\color[rgb]{.359375, .59765625, .41015625}{#1}}}
\\newcommand\\gray[1]{{\\color[rgb]{0.5, 0.5, 0.5}{#1}}}
\\newcommand\\purple[1]{{\\color[rgb]{0.63515625, 0.49609375, 0.80859375}{#1}}}
\\newcommand\\white[1]{{\\color{white}{#1}}}
\\newcommand\\orange[1]{{\\color[rgb]{0.63515625, 0.51015625, 0.37734375}{#1}}}
$$

"""

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python convert_segment.py input.tex output.md")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2]
    
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Convert content
    converted = convert_latex_to_marp(content)
    
    # Add Marp header
    result = create_marp_header() + converted
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(result)
    
    print(f"Converted {input_file} to {output_file}")
