#!/bin/bash
# <PERSON>ript to merge converted-n.md files into a single converted.md file
# Following tex2marp rules for directive management

OUTPUT_FILE="converted.md"

# Start with the first file's complete content (including global directives)
echo "Merging converted files..."
cp converted-1.md "$OUTPUT_FILE"

# For subsequent files, extract only content after the global directives
for i in {2..13}; do
    FILE="converted-$i.md"
    if [ -f "$FILE" ]; then
        echo "Adding content from $FILE..."
        
        # Find the line number where the second --- appears (end of frontmatter)
        FRONTMATTER_END=$(awk '/^---/{i++}i==2{print NR; exit}' "$FILE")
        
        if [ -n "$FRONTMATTER_END" ]; then
            # Add content after the frontmatter, skipping the math definitions block
            tail -n +$((FRONTMATTER_END + 1)) "$FILE" | sed '/^\$\$/,/^\$\$/d' >> "$OUTPUT_FILE"
        else
            # Fallback: just append the whole file content
            echo "" >> "$OUTPUT_FILE"
            cat "$FILE" >> "$OUTPUT_FILE"
        fi
    else
        echo "Warning: $FILE not found"
    fi
done

echo "Merge complete. Output saved to $OUTPUT_FILE"
echo "Total lines in merged file: $(wc -l < "$OUTPUT_FILE")"
