---
description: This file describes the rules for the conversion of .tex file to .md file for building slides with marp.
---


# Files
- `slides-example.md`: style examples
- `image/` or `images/`: folder for images

# General rules
- Read the `slides-example.md` file for style examples, follow the global directive shown in the file
- DO NOT change the content of the original `.tex` file, only convert the syntax and format into marp syntax

# Detailed replacemenet rules

## convert colored box
(the double star should be included in the replacement, with NO SPACE between the stars and the content)
- `\greenbox{green text}` -> `**<green>green text**`
- `\redbox{green text}` -> `**<red>green text**`
- `\bluebox{green text}` -> `**<orange>green text**`

## convert comment block
- `\commentblock{COMMENT_TITLE}{COMMENT_CONTENT}` should be replaced by (notice an EMPTY line should be added AFTER the `<div class='proof comment'>`)

  ```
  <div class='proof comment'>

  **COMMENT_TITLE**

  COMMENT_CONTENT
  </div>
  ```

- In `\commentblock{COMMENT_TITLE}{COMMENT_CONTENT}`, there are also colored boxes, `\greenbox{text}`, `\bluebox{text}`, `\redbox{text}`, also replaceable by `**<green>green text**`, `**<red>red text**`, `**<orange>orange text**`

## convert exposition block
- `\expositionblock{TITLE}{CONTENT}` should be replaced by (notice an EMPTY line should be added AFTER the <div class="proof">)

  ```
  <div class="proof">

  **TITLE**

  CONTENT
  </div>
  ```

## convert highlight in math environment
- In math environment, replace `\highlight{red}{MATH}` by `\red{MATH}` 
- In math environment, replace `\highlight{titlegreen}{MATH}` by `\green{MATH}` 
- In math environment, replace `\highlight{titleblue}{MATH}` by `\orange{MATH}`
- DO NOT CHANGE THE CONTENT OF THE MATH EQUATION, KEEP THE `\begin{align}...\end{align}` and the alignment points `&=`, `=&`, etc.

## convert image
- replace `\includegraphics[width=XX\textwidth]{URL}` with

  ```
  ![width:YYpx](URL)
  ```

  adjust YY based on XX and the width of the slide


## convert pdf to svg
- There could pdf images imported in the .tex file, they should be first converted into .svg files
- Use `inkscape` to convert the pdf files in the `image` or `images` folder, by the following command:
  ```
  for pdf in *.pdf; do inkscape --without-gui --file="$pdf" --export-plain-svg --export-filename="${pdf%.pdf}.svg"; done
  ```
- reference the svg files in the .md file
  ```
  ![width:YYpx](URL)
  ```