\begin{frame}{}
	\frametitle{割线：例}
	\transfade
	\begin{itemize}
		\item<+-> 更一般地，对 $x < 0$，割线的上岸和下岸的值已被确定，
		\begin{align}
			f(x + i \epsilon) = i |x|^{\frac{1}{2}}, \qquad
			f(x - i \epsilon) = - i |x|^{\frac{1}{2}} \ .
		\end{align}
		\commentblock{割线}{
		割线上的点已经被舍弃，$f(x < 0)$ \bluebox{无定义}。
		}
	\end{itemize}

\end{frame}




\begin{frame}{}
	\frametitle{其它常见多值函数}
	\transfade
	\begin{itemize}
		\item<+-> 多值函数无处不在，除了根式函数，还有
		\begin{itemize}
			\item<+-> 一般幂次函数 $z^\alpha$，$\alpha \in \mathbb{C}$
			\item<+-> 对数函数：$\ln (z - a)$
			\item<+-> 反三角函数 $\arcsin z$, $\arccos$, $\arctan$ 等
			\item<+-> 单值函数、多值函数的各种复合，如
			\begin{equation}
				\left(\frac{z - a}{z - b}\right)^\alpha, \qquad
				\ln \left(z^\alpha + (1 - z^2)^\beta\right) \ , \qquad \cdots \ .
			\end{equation}
		\end{itemize}
	\end{itemize}

\end{frame}





\begin{frame}{}
	\frametitle{其他空间上的复分析}
	\transfade
	\begin{itemize}
		\item<+-> 除了割线，还有别的方法治理多值性问题：多叶黎曼面 (Riemann surface)
		\item<+-> 以 $f(z) = \sqrt{z}$ 为例：原点是一个 \bluebox{支点}，绕原点一圈会有多值现象
		\begin{equation}
			f(e^{2\pi i}z) = - f(z) \ .
		\end{equation}
		\item<+-> 杜绝多值性的老方法：画割线，禁止穿越
		\item<+-> 杜绝多值性的新方法：\bluebox{扩展定义域}，把 $e^{2\pi i}z$ 与 $z$ 看成两个 \bluebox{不同的点}，但同时尊重 \bluebox{连续性}
	\end{itemize}

\end{frame}

\begin{frame}[t]\frametitle{其它他间上的复分析}
\begin{itemize}
	\item<+-> 先只关注单位圆，把单位圆所有点复制一遍
\end{itemize}
\begin{figure}
	\centering


	\tikzset{every picture/.style={line width=0.75pt}} %set default line width to 0.75pt        

	\begin{tikzpicture}[x=0.75pt,y=0.75pt,yscale=-1,xscale=1, scale=0.6]
	%uncomment if require: \path (0,300); %set diagram left start at 0, and has height of 300

	%Shape: Circle [id:dp7727328764543693] 
	\draw  [dash pattern={on 4.5pt off 4.5pt}] (56,145.88) .. controls (56,112.81) and (82.81,86) .. (115.88,86) .. controls (148.95,86) and (175.76,112.81) .. (175.76,145.88) .. controls (175.76,178.95) and (148.95,205.76) .. (115.88,205.76) .. controls (82.81,205.76) and (56,178.95) .. (56,145.88) -- cycle ;
	%Shape: Circle [id:dp02247454581082553] 
	\draw  [fill={rgb, 255:red, 0; green, 0; blue, 0 }  ,fill opacity=1 ] (111.88,145.88) .. controls (111.88,143.67) and (113.67,141.88) .. (115.88,141.88) .. controls (118.09,141.88) and (119.88,143.67) .. (119.88,145.88) .. controls (119.88,148.09) and (118.09,149.88) .. (115.88,149.88) .. controls (113.67,149.88) and (111.88,148.09) .. (111.88,145.88) -- cycle ;
	%Shape: Circle [id:dp4751283197599847] 
	\draw  [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=1 ][fill={rgb, 255:red, 245; green, 166; blue, 35 }  ,fill opacity=1 ] (171.76,145.88) .. controls (171.76,143.67) and (173.55,141.88) .. (175.76,141.88) .. controls (177.97,141.88) and (179.76,143.67) .. (179.76,145.88) .. controls (179.76,148.09) and (177.97,149.88) .. (175.76,149.88) .. controls (173.55,149.88) and (171.76,148.09) .. (171.76,145.88) -- cycle ;
	%Shape: Arc [id:dp5913150652123564] 
	\draw  [draw opacity=0] (159.28,115.28) .. controls (161.73,118.76) and (163.8,122.59) .. (165.4,126.74) .. controls (167.7,132.69) and (168.86,138.8) .. (168.98,144.83) -- (115.88,145.88) -- cycle ; \draw  [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=1 ] (159.28,115.28) .. controls (161.73,118.76) and (163.8,122.59) .. (165.4,126.74) .. controls (167.7,132.69) and (168.86,138.8) .. (168.98,144.83) ;  
	\draw  [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=1 ][fill={rgb, 255:red, 245; green, 166; blue, 35 }  ,fill opacity=1 ] (159.84,123.42) -- (159.23,114.5) -- (166.74,119.36) -- (161.26,117.95) -- cycle ;
	%Shape: Circle [id:dp28687598323513197] 
	\draw  [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=0.07 ][fill={rgb, 255:red, 245; green, 166; blue, 35 }  ,fill opacity=0.5 ] (167.09,170.21) .. controls (167.09,168) and (168.88,166.21) .. (171.09,166.21) .. controls (173.3,166.21) and (175.09,168) .. (175.09,170.21) .. controls (175.09,172.42) and (173.3,174.21) .. (171.09,174.21) .. controls (168.88,174.21) and (167.09,172.42) .. (167.09,170.21) -- cycle ;
	%Shape: Arc [id:dp5066432149780815] 
	\draw  [draw opacity=0] (168.61,152.15) .. controls (167.69,159.88) and (165.07,167.3) .. (161,173.87) -- (115.88,145.88) -- cycle ; \draw  [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=0.5 ] (168.61,152.15) .. controls (167.69,159.88) and (165.07,167.3) .. (161,173.87) ;  
	\draw  [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=0.09 ][fill={rgb, 255:red, 245; green, 166; blue, 35 }  ,fill opacity=0.5 ] (163.53,156.08) -- (169.23,149.18) -- (171.32,157.88) -- (168.33,153.08) -- cycle ;
	%Shape: Ellipse [id:dp24814240841205115] 
	\draw   (326.95,126.67) .. controls (326.95,115.62) and (357.47,106.67) .. (395.12,106.67) .. controls (432.77,106.67) and (463.29,115.62) .. (463.29,126.67) .. controls (463.29,137.71) and (432.77,146.67) .. (395.12,146.67) .. controls (357.47,146.67) and (326.95,137.71) .. (326.95,126.67) -- cycle ;
	%Shape: Ellipse [id:dp7802088486615635] 
	\draw   (326.95,176.67) .. controls (326.95,165.62) and (357.47,156.67) .. (395.12,156.67) .. controls (432.77,156.67) and (463.29,165.62) .. (463.29,176.67) .. controls (463.29,187.71) and (432.77,196.67) .. (395.12,196.67) .. controls (357.47,196.67) and (326.95,187.71) .. (326.95,176.67) -- cycle ;
	%Shape: Circle [id:dp8196552141410924] 
	\draw  [fill={rgb, 255:red, 0; green, 0; blue, 0 }  ,fill opacity=1 ] (391.12,126.67) .. controls (391.12,124.46) and (392.91,122.67) .. (395.12,122.67) .. controls (397.33,122.67) and (399.12,124.46) .. (399.12,126.67) .. controls (399.12,128.88) and (397.33,130.67) .. (395.12,130.67) .. controls (392.91,130.67) and (391.12,128.88) .. (391.12,126.67) -- cycle ;
	%Shape: Circle [id:dp6696435217641126] 
	\draw  [fill={rgb, 255:red, 0; green, 0; blue, 0 }  ,fill opacity=1 ] (391.12,176.67) .. controls (391.12,174.46) and (392.91,172.67) .. (395.12,172.67) .. controls (397.33,172.67) and (399.12,174.46) .. (399.12,176.67) .. controls (399.12,178.88) and (397.33,180.67) .. (395.12,180.67) .. controls (392.91,180.67) and (391.12,178.88) .. (391.12,176.67) -- cycle ;
	%Shape: Circle [id:dp609227020765152] 
	\draw  [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=1 ][fill={rgb, 255:red, 245; green, 166; blue, 35 }  ,fill opacity=1 ] (459.29,126.67) .. controls (459.29,124.46) and (461.08,122.67) .. (463.29,122.67) .. controls (465.5,122.67) and (467.29,124.46) .. (467.29,126.67) .. controls (467.29,128.88) and (465.5,130.67) .. (463.29,130.67) .. controls (461.08,130.67) and (459.29,128.88) .. (459.29,126.67) -- cycle ;
	%Shape: Circle [id:dp765601205345209] 
	\draw  [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=1 ][fill={rgb, 255:red, 245; green, 166; blue, 35 }  ,fill opacity=1 ] (459.29,176.67) .. controls (459.29,174.46) and (461.08,172.67) .. (463.29,172.67) .. controls (465.5,172.67) and (467.29,174.46) .. (467.29,176.67) .. controls (467.29,178.88) and (465.5,180.67) .. (463.29,180.67) .. controls (461.08,180.67) and (459.29,178.88) .. (459.29,176.67) -- cycle ;

	% Text Node
	\draw (184.76,139.28) node [anchor=north west][inner sep=0.75pt]  [color={rgb, 255:red, 139; green, 87; blue, 42 }  ,opacity=1 ]  {$选择f( 1) =1$};
	% Text Node
	\draw (173.09,173.61) node [anchor=north west][inner sep=0.75pt]  [color={rgb, 255:red, 139; green, 87; blue, 42 }  ,opacity=1 ]  {$f\left( e^{2\pi i} 1\right) =-1$};
	% Text Node
	\draw (115.88,153.28) node [anchor=north] [inner sep=0.75pt]    {$f( 0) =0$};
	% Text Node
	\draw (460,130.07) node [anchor=north west][inner sep=0.75pt]    {$f\left( e^{2\pi i} 1\right) =-1$};
	% Text Node
	\draw (465.29,180.07) node [anchor=north west][inner sep=0.75pt]    {$f( 1) =1$};


	\end{tikzpicture}
	\begin{center}
		\includegraphics[width=0.8\textwidth]{image/volcano.png}
	\end{center}
\end{figure}


\end{frame}



\begin{frame}[t]\frametitle{其它他间上的复分析}
\begin{itemize}
	\item<+-> 两个原点合同，因为 $f(z)$ 在原点没有多值性
	\commentblock{极简主义}{
	数学家崇尚 \bluebox{极简主义}，不引入非必要的东西。$f(z)$ 在原点处本身 $f(0) = 0$，\redbox{没有多值} 现象，\redbox{不需要} 复制多一份原点。
	}
\end{itemize}

\end{frame}



\begin{frame}[t]\frametitle{其它他间上的复分析}
\begin{itemize}
	\item<+-> 为了保持连续性，把两个单位圆连接起来
\end{itemize}
\begin{center}
	\includegraphics[width=0.7\textwidth]{image/connect.jpg}
\end{center}
\end{frame}



\begin{frame}

\begin{center}
	\href{./animation/DoubleCircle.html}{\includegraphics[width=0.8\textwidth]{DoubleCircle.png}}
\end{center}
	
\end{frame}



\begin{frame}[t]\frametitle{其它他间上的复分析}
\begin{itemize}
	\item<+-> 为了保持连续性，把两个单位圆连接起来
\end{itemize}
\begin{figure}
	\centering
	\tikzset{every picture/.style={line width=0.75pt}} %set default line width to 0.75pt        

	\begin{tikzpicture}[x=0.75pt,y=0.75pt,yscale=-1,xscale=1]
	%uncomment if require: \path (0,300); %set diagram left start at 0, and has height of 300

	%Shape: Circle [id:dp9081192641928111] 
	\draw  [fill={rgb, 255:red, 0; green, 0; blue, 0 }  ,fill opacity=1 ] (176.12,94.67) .. controls (176.12,92.46) and (177.91,90.67) .. (180.12,90.67) .. controls (182.33,90.67) and (184.12,92.46) .. (184.12,94.67) .. controls (184.12,96.88) and (182.33,98.67) .. (180.12,98.67) .. controls (177.91,98.67) and (176.12,96.88) .. (176.12,94.67) -- cycle ;
	%Shape: Circle [id:dp8343641741298735] 
	\draw  [fill={rgb, 255:red, 0; green, 0; blue, 0 }  ,fill opacity=1 ] (176.12,144.67) .. controls (176.12,142.46) and (177.91,140.67) .. (180.12,140.67) .. controls (182.33,140.67) and (184.12,142.46) .. (184.12,144.67) .. controls (184.12,146.88) and (182.33,148.67) .. (180.12,148.67) .. controls (177.91,148.67) and (176.12,146.88) .. (176.12,144.67) -- cycle ;
	%Shape: Circle [id:dp519628372458337] 
	\draw  [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=1 ][fill={rgb, 255:red, 245; green, 166; blue, 35 }  ,fill opacity=1 ] (244.29,94.67) .. controls (244.29,92.46) and (246.08,90.67) .. (248.29,90.67) .. controls (250.5,90.67) and (252.29,92.46) .. (252.29,94.67) .. controls (252.29,96.88) and (250.5,98.67) .. (248.29,98.67) .. controls (246.08,98.67) and (244.29,96.88) .. (244.29,94.67) -- cycle ;
	%Shape: Circle [id:dp3109743739525104] 
	\draw  [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=1 ][fill={rgb, 255:red, 245; green, 166; blue, 35 }  ,fill opacity=1 ] (244.29,144.67) .. controls (244.29,142.46) and (246.08,140.67) .. (248.29,140.67) .. controls (250.5,140.67) and (252.29,142.46) .. (252.29,144.67) .. controls (252.29,146.88) and (250.5,148.67) .. (248.29,148.67) .. controls (246.08,148.67) and (244.29,146.88) .. (244.29,144.67) -- cycle ;
	%Straight Lines [id:da018876663471852684] 
	\draw    (180.12,94.67) -- (180.12,144.67) ;
	%Straight Lines [id:da7037334539323612] 
	\draw    (180.12,144.67) -- (180.12,169.87) ;
	%Straight Lines [id:da4157725534620731] 
	\draw    (180.12,64.27) -- (180.12,94.67) ;
	%Curve Lines [id:da5395815094655585] 
	\draw [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=1 ]   (248.29,144.67) .. controls (250.13,118.52) and (112.13,119.32) .. (111.95,144.67) .. controls (111.76,170.01) and (221.45,168.98) .. (235.16,153.84) .. controls (248.88,138.7) and (248.72,120.13) .. (248.29,94.67) .. controls (247.85,69.21) and (112.35,69.46) .. (111.95,94.67) .. controls (111.54,119.88) and (221.21,116.54) .. (234.88,106.7) .. controls (248.55,96.85) and (238.63,149.05) .. (240.33,153.83) .. controls (242.04,158.61) and (244.33,155.12) .. (246.48,149.83) ;
	\draw  [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=1 ][fill={rgb, 255:red, 245; green, 166; blue, 35 }  ,fill opacity=1 ] (200.29,130.02) -- (192.63,125.41) -- (200.91,122.04) -- (196.61,125.72) -- cycle ;
	\draw  [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=1 ][fill={rgb, 255:red, 245; green, 166; blue, 35 }  ,fill opacity=1 ] (243.83,119.94) -- (248.06,112.06) -- (251.83,120.17) -- (247.95,116.06) -- cycle ;
	\draw  [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=1 ][fill={rgb, 255:red, 245; green, 166; blue, 35 }  ,fill opacity=1 ] (210.96,81.02) -- (203.29,76.41) -- (211.58,73.04) -- (207.28,76.72) -- cycle ;
	\draw  [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=1 ][fill={rgb, 255:red, 245; green, 166; blue, 35 }  ,fill opacity=1 ] (162.52,109.5) -- (170.27,113.96) -- (162.05,117.48) -- (166.28,113.72) -- cycle ;
	\draw  [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=1 ][fill={rgb, 255:red, 245; green, 166; blue, 35 }  ,fill opacity=1 ] (158.85,160.16) -- (166.61,164.62) -- (158.39,168.15) -- (162.61,164.39) -- cycle ;
	\draw  [color={rgb, 255:red, 245; green, 166; blue, 35 }  ,draw opacity=1 ][fill={rgb, 255:red, 245; green, 166; blue, 35 }  ,fill opacity=1 ] (245.97,121.83) -- (241.38,129.51) -- (237.99,121.24) -- (241.68,125.52) -- cycle ;

	% Text Node
	\draw (250.29,98.07) node [anchor=north west][inner sep=0.75pt]    {$f\left( e^{2\pi i} 1\right) =-1$};
	% Text Node
	\draw (250.29,148.07) node [anchor=north west][inner sep=0.75pt]    {$f( 1) =1$};
	% Text Node
	\draw (157.63,186.4) node [anchor=north west][inner sep=0.75pt]   [align=left] {第二步};


	\end{tikzpicture}
	\includegraphics[width=0.4\textwidth]{image/two-sheeted.png}
\end{figure}

    
\commentblock{一阶支点}{
	注意原点是一阶支点，\bluebox{转两圈}，多值性消失。
}

\end{frame}



\begin{frame}[t]\frametitle{其他空间上的复分析}
\begin{itemize}
	\item<+-> $\sqrt{z}$ 对应的 Riemann surface
\end{itemize}
\begin{figure}
	\centering
	\includegraphics[width=0.4\textwidth]{image/sqrt.svg.png}
	\caption{双叶}
\end{figure}

\end{frame}
