\begin{frame}{}
	\frametitle{复数的四则运算：例子}
	\transfade
	\begin{itemize}
		\item<+-> 考虑一个虚部为零的复数 (其实就是实数) $z_a \coloneqq a + 0 i$，一个纯虚数 $z_b \coloneqq 0 + bi$，则它们的和
		\begin{align}
			z_a + z_b = (a + 0) + (0 + b)i = a + b i \in \mathbb{C}\ .
		\end{align}
		\commentblock{复数的分解}{
		任何一个复数都可以分解为一个 \bluebox{实数} 和一个 \bluebox{纯虚数} 的和。
		}
	\end{itemize}
\end{frame}



\begin{frame}{}
	\frametitle{复数的代数运算}
	\transfade
	\begin{itemize}
		\item<+-> 理解这些四则运算有两种思路。

		\item<+-> (1) 把这些 \bluebox{具体的式子} 作为加减乘除运算的 \bluebox{定义}，可以验证它们满足之前列举的所有性质 (交换律、结合律、分配律等)

		\item<+-> (2) 把运算应满足的 \bluebox{规律} (交换律、结合律等) 作为出发点、\bluebox{定义}，则上面这些具体的运算规则可以被推导出来，从而获得复数及其运算。
	\end{itemize}
\end{frame}




\begin{frame}{}
	\frametitle{复数的代数运算}
	\transfade
	
	\commentblock{定义对象的新思路}{
	"It is \redbox{not} who I am underneath, but \bluebox{what I do}，that defines me."

	\rightline{——著名慈善家、企业家 Bruce Wayne}
	}
	\begin{figure}
		\includegraphics[width=0.9\textwidth]{image/Bruce.jpg}
	\end{figure}
\end{frame}


\begin{frame}{}
	\frametitle{复数的代数运算}
	\transfade
	\begin{itemize}
		\item<+-> 复数乘法定义实际上就是 \bluebox{分配律}、\bluebox{交换律}、\bluebox{结合律} 的结果：把 $a_{1,2} + b_{1,2} i$ 均看成是 \bluebox{实数} \bluebox{加} \bluebox{纯虚数}，
		\begin{align}
			(a_1 + b_1 i) (a_2 + b_2 i)
			= & \ 
			a_1 a_2 + b_1 i b_2 i + a_1 b_2 i + b_1 i a_2 \\
			= & \ a_1 a_2 + b_1 b_2 \orange{i^2} + (a_1 b_2 + b_1 a_2) i \\
			= & \ a_1 a_2 \orange{-} b_1 b_2 + (a_1 b_2 + b_1 a_2) i
		\end{align}
	\end{itemize}
\end{frame}


\begin{frame}{}
	\frametitle{复数的代数运算}
	\transfade
	\begin{itemize}
		\item<+-> $z$ 的模方 (modular-square)
		\begin{align}
			z \bar z = z z^* = (x + i y)(x - i y) = x^2 + y^2 \ .
		\end{align}
		\commentblock{因式分解}{
		$x^2 + y^2$ 可以在复数范围内作因式分解：原本不可能的事情，在复数范围内变得可能。
		}
		\item<+-> \greenbox{幂次运算}：对于 $n = 0, 1,2, 3, \dots$，
		\begin{equation}
			z^n = z \cdot z \cdot ... \cdot z \ .
		\end{equation}
		\item<+-> 常用结果：$i^{2k} = (-1)^k$，$i^{4k} = 1$, $k \in \mathbb{N}$
	\end{itemize}
\end{frame}




\begin{frame}{}
	\frametitle{复数的表示}
	\transfade
	\begin{itemize}
		\item<+-> \bluebox{代数式表示/定义}：对任意 $z \in \mathbb{C}$，存在 $x, y \in \mathbb{R}$ 使得
		\begin{align}
			z = x + y i \ .
		\end{align}
		\item<+-> \bluebox{矢量表示}：对任意 $z = x + yi \in \mathbb{C}$，可以在 $\mathbb{R}^2$ 上找到一个点/矢量 $(x,y)$ 对应

		允许矢量平移，矢量加减法 $=$ 复数加减法

		\begin{center}
			\href{./animation/Vector_rep.html}{\includegraphics[width=0.4\textwidth]{image/vector-rep.jpg}}
		\end{center}
	\end{itemize}
\end{frame}


\begin{frame}{}
	\frametitle{复数的表示}
	\transfade
	\begin{itemize}
		\item<+-> \bluebox{三角表示法}：用极坐标来标记矢量表示法中的 $x,y$，
		\begin{align}
			x = r \cos \theta, \quad y = r \sin \theta \qquad \Rightarrow
			\qquad
			z = & \ r \cos \theta + i r \sin \theta\\
			= & \ r(\cos\theta + i \sin \theta)\ .
		\end{align}
		角度 $\theta \in \mathbb{R}$ 称为 \greenbox{辐角 $\operatorname{arg} z$}，长度 $r$ 称为 \greenbox{模 (modulus) $|z|$}, 也称为绝对值。
		\begin{align}
			r = |z| = \sqrt{x^2 + y^2} \ .
		\end{align}
		
		\begin{center}
			\href{./animation/Trigonometric_Rep.html}{
				\includegraphics[width=0.3\textwidth]{Trig-Rep.png}
			}
		\end{center}
	\end{itemize}
\end{frame}





\begin{frame}{}
	\frametitle{复数的表示}
	\transfade
	\begin{itemize}

		\item<+-> \bluebox{指数表示法}：\greenbox{$e^{i\theta} \coloneqq \cos \theta + i \sin \theta$}, 从而
		\begin{align}
			z = r e^{i \theta} = r \exp \left[i \theta\right] \ .
		\end{align}

		\begin{center}
			\href{./animation/Exponential_Rep.html}{
				\includegraphics[width=0.5\textwidth]{Exponential_Rep.png}
			}
		\end{center}
	\end{itemize}
\end{frame}



\begin{frame}{}
	\frametitle{复数的表示}
	\transfade
	\commentblock{纯虚数的指数的理解}{
	\visible<+->{注意 $\theta$ 是 \bluebox{实数}，$i \theta$ 是 \bluebox{纯虚数}。$i\theta$ 的指数应该通过 Taylor 展开，}
	\begin{align}
		e^{i \theta} = \sum_{n = 0}^{+\infty} \frac{1}{n!} (i \theta)^n
		= & \ \visible<+->{ \sum_{k = 0}^{+\infty} \frac{1}{(2k)!} (i \theta)^{2k}
		+ \sum_{k = 0}^{+\infty} \frac{1}{(2k + 1)!} (i \theta)^{2k + 1}}
		\nonumber\\
		= & \ \visible<+->{\sum_{k = 0}^{+\infty} \frac{1}{(2k)!} i^{2k} \theta^{2k}
		+ \sum_{k = 0}^{+\infty} \frac{1}{(2k + 1)!} i^{2k + 1} \theta^{2k + 1}}\nonumber\\
		= & \ \visible<+->{\sum_{k = 0}^{+\infty} \frac{1}{(2k)!} (-1)^k  \theta^{2k} 
		+ i \sum_{k = 0}^{+\infty} \frac{1}{(2k + 1)!} (-1)^k \theta^{2k + 1}}\nonumber\\
		= & \visible<+->{ \cos \theta + i \sin \theta} \ .
	\end{align}
	}
\end{frame}




\begin{frame}{}
	\frametitle{复数的表示：辐角多值性}
	\transfade
	\begin{itemize}
		\item<+-> 三角表示和指数表示中都涉及 \bluebox{辐角}。
		\item<+-> 给定一个非零 $z$，其辐角 \redbox{不唯一}。对任意 \greenbox{整数 $k$}，都有
		\begin{align}
			z = r (\cos \theta + i \sin \theta)
			= r (\cos (\theta + 2\pi k) + i \sin (\theta + 2\pi k)) \ .
		\end{align}
		因此，指数表达式也不唯一，
		\begin{align}
			z = re^{i \theta} = r e^{i (\theta + 2\pi k)} \ .
		\end{align}
		\item<+-> 有时候，人们会 \bluebox{人为限制} $\theta$ 的一个取值范围，如 $\theta \in [0, 2\pi)$
	\end{itemize}
\end{frame}
